.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    animation: slideIn 0.3s ease-out;

    h2 {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        text-align: center;
        margin-bottom: 20px;
    }

    p {
        font-size: 16px;
        color: #666;
        text-align: center;
        margin-bottom: 10px;
    }
}

.modal-close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
        color: #000;
    }
}

.form-group {
    margin-bottom: 20px;

    label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #555;
        margin-bottom: 8px;
    }

    input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        color: #333;
        outline: none;
        transition: border-color 0.2s;
        box-sizing: border-box;

        &:focus {
            border-color: #4caf50;
        }

        &::placeholder {
            color: #aaa;
        }

        &:disabled {
            background: #f5f5f5;
            cursor: not-allowed;
        }
    }
}

.code-inputs {
    margin-bottom: 20px;

    label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #555;
        margin-bottom: 8px;
    }

    .code-input-container {
        display: flex;
        justify-content: space-between;
        gap: 10px;
    }

    .code-input {
        width: 50px;
        height: 50px;
        text-align: center;
        font-size: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 0;
        text-transform: uppercase;

        &:focus {
            border-color: #4caf50;
        }

        &:disabled {
            background: #f5f5f5;
            cursor: not-allowed;
        }
    }
}

.confirm-submit-btn {
    width: 100%;
    padding: 12px;
    background: #007bff;
    border: none;
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
    margin-bottom: 10px;

    &:hover {
        background: #0056b3;
    }

    &:disabled {
        background: #cccccc;
        cursor: not-allowed;
    }
}

.resend-btn {
    width: 100%;
    padding: 12px;
    background: #4caf50;
    border: none;
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;

    &:hover {
        background: #388e3c;
    }

    &:disabled {
        background: #cccccc;
        cursor: not-allowed;
    }
}

.confirm-success {
    text-align: center;

    h2 {
        font-size: 20px;
        color: #007bff;
        margin-bottom: 20px;
    }

    .success-icon {
        font-size: 40px;
        color: #007bff;
    }
}

.error-message {
    color: #d32f2f;
    font-size: 14px;
    margin-bottom: 15px;
    text-align: center;
}

.success-message {
    color: #388e3c;
    font-size: 14px;
    margin-bottom: 15px;
    text-align: center;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}