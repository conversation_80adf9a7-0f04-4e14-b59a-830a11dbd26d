// src/components/Layout.scss
.layout-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  // Thêm padding-top để tránh nội dung bị che bởi header cố định
  // Header có height khoảng 70-80px, thêm một chút buffer
  padding-top: 0; // Không cần padding vì Intro component đã có background image full height
  
  // Đ<PERSON><PERSON> bảo nội dung không bị overflow
  overflow-x: hidden;
}

// Responsive adjustments
@media (max-width: 768px) {
  .main-content {
    padding-top: 0; // Mobile cũng không cần padding
  }
}
