// src/components/Layout.jsx
import React from "react";
import Header from "./Header";
import UserLoginManagement from "./UserLoginManagement";
import { Outlet } from "react-router-dom";
import "./Layout.scss";
import Footer from "./Footer";

const Layout = () => {
  return (
    <div className="layout-container">
      <Header />
      <UserLoginManagement />
      <main className="main-content">
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
