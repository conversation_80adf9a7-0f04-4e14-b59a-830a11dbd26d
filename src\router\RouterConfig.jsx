import { createBrowserRouter } from "react-router-dom";
import HomePage from "../pages/HomePage"; // Đ<PERSON><PERSON> bảo đường dẫn này đúng
import { AuthWrapper } from "../context/AuthProvider";
import Room from "../components/Room";

// Đ<PERSON><PERSON> nghĩa các route
const router = createBrowserRouter([
  {
    path: "/",
    element: (
      <AuthWrapper>
        <HomePage />
      </AuthWrapper>
    ),
  },
  {
    path: "/room",
    element: <Room />,
  },
  // {
  //     path: '/room',
  //     element: <Room />,
  // },
  // {
  //     path: '/service',
  //     element: <Service />,
  // },
  // {
  //     path: '/contact',
  //     element: <Contact />,
  // },
  // {
  //     path: '/about-us',
  //     element: <AboutUs />,
  // },
]);

export default router;
