import { createBrowserRouter } from "react-router-dom";
import HomePage from "../pages/HomePage"; // Đảm bảo đường dẫn này đúng
import { AuthWrapper } from "../context/AuthProvider";
import Room from "../components/Room";
import Layout from "../components/Layout";

// Đ<PERSON>nh nghĩa các route
const router = createBrowserRouter([
  {
    path: "/",
    element: (
      <AuthWrapper>
        <Layout />
      </AuthWrapper>
    ),
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: "room",
        element: <Room />,
      },
      // {
      //     path: 'service',
      //     element: <Service />,
      // },
      // {
      //     path: 'contact',
      //     element: <Contact />,
      // },
      // {
      //     path: 'about-us',
      //     element: <AboutUs />,
      // },
    ],
  },
]);

export default router;
