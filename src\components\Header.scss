.header {
  font-family: 'Roboto', sans-serif;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  position: fixed; // <PERSON><PERSON> định header
  top: 0;
  left: 0;
  box-sizing: border-box;

  width: 100%;
  z-index: 1000;
  background-color: transparent; // Trong suốt ban đầu
  transition: background-color 0.3s ease; // Hiệu ứng chuyển màu mượt mà

  &.scrolled {
    background-color: #202020; // Màu khi scroll xuống
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); // Thêm bóng để nổi bật
  }

  .nav-logo {
    .logo {
      font-size: 1.3rem;
      font-weight: bold;
      color: #fff; // Màu trắng ban đầu để nổi trên background-image
      transition: color 0.3s ease;
      text-align: center;




      .header.scrolled & {
        color: #333; // Màu tối khi scroll xuống
      }
    }
  }

  .nav-list {
    ul {
      display: flex;
      list-style: none;
      gap: 2rem;
      margin: 0;
      padding: 0;

      li {
        .nav-link {
          font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          text-decoration: none;
          color: #fff; // Màu trắng ban đầu
          font-weight: 500;
          font-size: 1rem;
          transition: color 0.3s ease;

          &:hover {
            color: #007bff;
          }

          .header.scrolled & {
            color: #333; // Màu tối khi scroll xuống
          }
        }
      }
    }
  }

  .auth-buttons {
    display: flex;
    gap: 0.5rem;

    ul {
      list-style: none;
      margin: 0;
      padding: 0;

      button {
        padding: 0.4rem 0.75rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.3s ease;

        &.btn-primary {
          background-color: #007bff;
          color: white;

          &:hover {
            background-color: #0056b3;
          }
        }

        &:not(.btn-primary) {
          background-color: transparent;
          color: #fff; // Màu trắng ban đầu
          border: 1px solid #fff;

          &:hover {
            background-color: #007bff;
            color: white;
          }

          .header.scrolled & {
            color: #007bff; // Màu xanh khi scroll xuống
            border: 1px solid #007bff;
          }
        }
      }
    }

    .user-profile {
      position: relative;

      .profile-info {
        display: flex;
        align-items: center;
        gap: 3.1rem; // Giảm gap để gọn hơn
        padding: 0.3rem;
        border: 1px solid rgba(255, 255, 255, 0.5); // Viền trắng mờ ban đầu
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.2); // Nền mờ ban đầu
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }

        .header.scrolled & {
          border: 1px solid #e0e0e0;
          background-color: white;

          &:hover {
            background-color: #f5f5f5;
          }
        }

        .avatar-container {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          overflow: hidden;
          border: 2px solid #007bff;

          .user-avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .user-details {
          display: flex;
          flex-direction: column;

          .username {
            font-weight: 600;
            font-size: 0.85rem;
            color: #fff; // Màu trắng ban đầu

            .header.scrolled & {
              color: #333; // Màu tối khi scroll
            }
          }

          .points {
            font-size: 0.75rem;
            color: #fff; // Màu trắng ban đầu
            font-weight: 500;

            .header.scrolled & {
              color: #007bff; // Màu xanh khi scroll
            }
          }
        }

        .dropdown-arrow {
          font-size: 0.7rem;
          color: #fff; // Màu trắng ban đầu

          .header.scrolled & {
            color: #666; // Màu xám khi scroll
          }
        }
      }

      .profile-dropdown {
        position: absolute;
        top: calc(100% + 5px);
        right: 0;
        width: 190px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        z-index: 100;
        overflow: hidden;

        ul {
          display: flex;
          flex-direction: column;
          width: 100%;

          li {
            width: 100%;
            transition: background-color 0.2s;

            &:hover {
              background-color: #f5f5f5;
            }

            a {
              display: block;
              padding: 0.75rem 1rem;
              color: #333;
              text-decoration: none;
              font-size: 0.9rem;
              width: 100%;
            }

            &.logout-item {
              border-top: 1px solid #e0e0e0;

              a {
                color: #dc3545;
              }

              &:hover {
                background-color: #ffeeee;
              }
            }
          }
        }
      }
    }
  }

  .hamburger {
    display: none;
    color: #fff; // Màu trắng ban đầu

    .header.scrolled & {
      color: #333; // Màu tối khi scroll
    }
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .header {
    padding: 0.5rem;

    .nav-list {
      position: fixed; // Cố định khi mở menu
      top: 100%;
      left: 0;
      width: 100%;
      background-color: rgba(248, 249, 250, 0.95); // Nền mờ khi mở
      z-index: 1000;
      height: 0;
      overflow: hidden;
      opacity: 0;
      transition: all 0.3s ease;

      &.active {
        height: auto;
        opacity: 1;
        padding-bottom: 1rem;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
      }

      ul {
        flex-direction: column;
        padding: 1rem;

        li {
          width: 100%;

          .nav-link {
            display: block;
            padding: 0.5rem 0;
          }
        }
      }
    }

    .auth-buttons {
      &.active {
        position: relative;
        background-color: transparent;
        padding: 1rem;
      }
    }

    .hamburger {
      display: block;
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      z-index: 1200;
      margin-left: 0.5rem;
    }
  }
}