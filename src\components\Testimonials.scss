.section {
    padding: 4rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #0f172a;
}

.testimonials {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 1rem;
    color: #64748b;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 1rem;
}

.author-name {
    font-weight: bold;
    color: #0f172a;
}

.testimonial-card {
    /* ...các style hiện tại... */
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}