.cta {
    background-color: #2563eb;
    /* var(--primary) */
    color: white;
    text-align: center;
    padding: 4rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.cta-title {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.cta-text {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary {
    background-color: white;
    color: #2563eb;
    /* var(--primary) */
}

.btn-secondary:hover {
    background-color: #f8fafc;
    /* var(--light) */
}