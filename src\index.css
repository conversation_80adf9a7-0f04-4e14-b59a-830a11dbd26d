/* src/index.css */
/* import 'bootstrap/dist/css/bootstrap.min.css';
import 'jquery';
import 'popper.js';
import 'bootstrap/dist/js/bootstrap.bundle.min'; */
/* body {
    margin: 0;
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
} */
body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

#root {
    display: flex;
    flex-direction: column;
    max-height: 500vh;
    max-width: 100%;
}

.home-container{
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main{
    flex: 1;
}
