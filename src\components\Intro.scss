.header-container {
  padding-block: 10rem 5rem;
  height: 470px;
  background-image: url("../assets/resort.jpg");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  color: white;
  text-align: center;
  overflow: hidden;
  position: relative; // Đ<PERSON> có sẵn, cần để absolute hoạt động

}



.booking-container {
  width: 100%;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.9);
  position: absolute; // Thêm position absolute
  bottom: 0; // Đặt sát đáy của header-container
  left: 0;
}



.booking-form {
  overflow: hidden;
  padding: 1rem;
  display: flex;
  gap: 1.5rem; // Reduced gap slightly to fit four inputs comfortably
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  background-color: var(--white);
  // border: 2px solid red;
  border-radius: 15px;
  box-shadow: 5px 5px 20px rgba(0, 0, 0, 0.1);
  max-width: 1200px;
  height: 40px;
  margin: 0 auto;
}

.input-group {
  display: flex;
  align-items: center;

  span {
    margin-right: 0.5rem;
    color: #333;
  }

  input {
    padding: 0.7rem;
    border: 1px solid #ccc;
    border-radius: 5px;
    outline: none;
    width: 180px; // Slightly reduced width to accommodate extra input
  }
}

.input-btn {
  margin-left: auto;
}

.btn {
  padding: 0.7rem 1.5rem;
  background-color: #ff0000;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;

  &:hover {
    background-color: #cc0000;
  }
}

// Responsive design
@media (max-width: 768px) {
  .header-container {
    min-height: 150vh;
    padding-block: 5rem 2rem;
  }

  .booking-form {
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
  }

  .input-group {
    width: 100%;
    flex: 1 1 220px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    input {
      width: 100%;
    }
  }

  .input-btn {
    margin-left: 0;
  }
}