{"name": "hotel-management-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.0", "@react-icons/all-files": "^4.1.0", "@reduxjs/toolkit": "^2.6.0", "antd": "^5.23.2", "axios": "^1.7.9", "bootstrap": "^4.6.0", "firebase": "^11.4.0", "jquery": "^3.7.1", "lucide-react": "^0.475.0", "popper.js": "^1.16.1", "react": "^18.3.1", "react-bootstrap": "^2.10.9", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "react-toastify": "^11.0.5", "scrollreveal": "^4.0.9", "toastify": "^2.0.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "sass": "^1.85.1", "sass-embedded": "^1.83.4", "vite": "^6.0.5"}}